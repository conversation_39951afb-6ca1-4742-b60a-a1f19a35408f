import React, { useState, useEffect } from "react";
import Chat<PERSON>rea from "@/components/chatArea";
import Sidebar from "@/components/sidebar";
import FeedBack from "@/components/feedBack";
import { useChat } from "@/hooks/useChat";
import { useParams, FormattedMessage } from "umi";
import styles from "./index.module.less";
import openBtn from "@/assets/openBtn.png";
import { ArrowDowmOutlined, ChatNew } from "@/components/icon";
import { Dropdown } from "antd";
import analytics from "@/utils/analytics";

export default function HomePage() {
  // 获取路由参数
  const { id: conversationIdFromUrl } = useParams();

  // 侧边栏展开/收起 - 从本地存储读取初始状态
  const [isSiderOpen, setIsSiderOpen] = useState(() => {
    const saved = localStorage.getItem('chat-sidebar-open');
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [isLevitateSiderOpen, setIsLevitateSiderOpen] = useState(false);
  const [hoverTimer, setHoverTimer] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const changeSiderOpenStatus = () => {
    setIsSiderOpen((v) => {
      const newValue = !v;
      localStorage.setItem('chat-sidebar-open', JSON.stringify(newValue));
      return newValue;
    });
  };

  const changeLevitateSiderOpenStatus = (isOpen) => {
    if (isOpen) {
      // 清除之前的定时器
      if (hoverTimer) {
        clearTimeout(hoverTimer);
        setHoverTimer(null);
      }
      setIsLevitateSiderOpen(true);
    } else {
      // 延迟收起
      const timer = setTimeout(() => {
        setIsLevitateSiderOpen(false);
        setHoverTimer(null);
      }, 300); // 300ms延迟
      setHoverTimer(timer);
    }
  };

  const handleSidebarMouseEnter = () => {
    // 鼠标进入Sidebar时，清除收起定时器
    if (hoverTimer) {
      clearTimeout(hoverTimer);
      setHoverTimer(null);
    }
  };

  const handleSidebarMouseLeave = () => {
    // 鼠标离开Sidebar时，立即收起
    changeLevitateSiderOpenStatus(false);
  };

  const {
    conversations,
    activeConversation,
    activeConversationId,
    isLoading,
    isGenerating,
    isLoadingChats,
    isLoadingMoreChats,
    hasMoreChats,
    isLoadingMoreMessages,
    sendMessage,
    regenerateMessage,
    selectConversation,
    createNewConversation,
    deleteConversation,
    loadMoreChats,
    loadMoreMessages,
    initializeChat,
    pauseGeneration,
    handleLike,
    handleDislike,
  } = useChat(conversationIdFromUrl);

  const handleDropdownOpenChange = (open) => {
    setDropdownOpen(open);
  };

  const popupRender = () => (
    <div className={styles.modeDropdown}>
      <div className={styles.modeItem} onClick={() => setDropdownOpen(false)}>Tendata AI Beta</div>
    </div>
  );

  const ChatMode = ({style}) => (
    <Dropdown popupRender={popupRender} trigger={["click"]} open={dropdownOpen} onOpenChange={handleDropdownOpenChange}>
      <div className={styles.modelName} style={style}>
        <span>Tendata AI Beta</span>
        <ArrowDowmOutlined />
      </div>
    </Dropdown>
  );

  useEffect(() => {
    initializeChat();
  }, [initializeChat]);

  return (
    <div className={styles.chatPage}>
      {/* 主侧边栏 */}
      <Sidebar
        isSiderOpen={isSiderOpen}
        onFold={changeSiderOpenStatus}
        conversations={conversations}
        activeConversationId={activeConversationId}
        onSelectConversation={selectConversation}
        onNewConversation={createNewConversation}
        onDeleteConversation={deleteConversation}
        isLoadingChats={isLoadingChats}
        isLoadingMoreChats={isLoadingMoreChats}
        hasMoreChats={hasMoreChats}
        onLoadMoreChats={loadMoreChats}
      />
      {/* 右侧主内容区 */}
      <div
        className={styles.contentRight}
        style={{ marginLeft: isSiderOpen ? 0 : 12 }}
      >
        <div className={styles.topLeft}>
          {/* 悬浮侧边栏 */}
          {!isSiderOpen ? (
            <div className={styles.hideBox}>
              <div
                className={styles.hoverArea}
                onMouseLeave={handleSidebarMouseLeave}
                onMouseEnter={() => changeLevitateSiderOpenStatus(true)}
              >
                <div className={styles.contentLeftCollapsedHotZone}>
                  <div className={styles.contentLeftCollapsedHotZoneContent}>
                    <div
                      className={styles.openBtn}
                      onClick={changeSiderOpenStatus}
                    >
                      <img src={openBtn} alt="openBtn" />
                      <div className={styles.hoverAreaContent} />
                    </div>
                  </div>
                </div>
                <div
                  className={`${styles.levitateContentLeft} ${isLevitateSiderOpen ? styles.levitateContentLeftOpen : ""}`}
                  onMouseEnter={handleSidebarMouseEnter}
                >
                  <Sidebar
                    isSiderOpen={true}
                    onFold={() => changeLevitateSiderOpenStatus(false)}
                    conversations={conversations}
                    activeConversationId={activeConversationId}
                    onSelectConversation={selectConversation}
                    onNewConversation={createNewConversation}
                    onDeleteConversation={deleteConversation}
                    isFloating={true}
                    isLoadingChats={isLoadingChats}
                    isLoadingMoreChats={isLoadingMoreChats}
                    hasMoreChats={hasMoreChats}
                    onLoadMoreChats={loadMoreChats}
                  />
                </div>
              </div>
              <div className={styles.conAddBtn} onClick={createNewConversation}>
                <ChatNew width={14} height={14} />
                <FormattedMessage id="chat-new" />
              </div>
              <div className={styles.line} />
              <ChatMode />
            </div>
          ): <ChatMode style={{position: 'absolute', top: '14px', left: '24px',zIndex: 10}} />}
          
        </div>
        <div
          className={`${styles.chatContainer} ${!isSiderOpen ? styles.chatContainerCollapsed : ""}`}
        >
          <ChatArea
            messages={activeConversation?.messages || []}
            onSendMessage={sendMessage}
            onRegenerate={regenerateMessage}
            onPause={pauseGeneration}
            isLoading={isLoading}
            isGenerating={isGenerating}
            onLoadMoreMessages={() => loadMoreMessages(activeConversationId)}
            isLoadingMoreMessages={isLoadingMoreMessages}
            hasMoreMessages={activeConversation?.hasMoreMessages}
            activeConversationId={activeConversationId}
            onLike={handleLike}
            onDislike={handleDislike}
          />
        </div>
        {/* 反馈 */}
        <FeedBack />
      </div>
    </div>
  );
}
