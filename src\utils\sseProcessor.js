/**
 * SSE 流处理工具函数
 */

/**
 * 处理 SSE 流数据
 * @param {ReadableStreamDefaultReader} reader - 流读取器
 * @param {Function} onChunk - 数据块回调函数
 * @param {AbortSignal} signal - 取消信号
 * @returns {Promise<Object|Error>} 返回最终结果或错误对象
 */
export async function processSSEStream(reader, onChunk, signal) {
  const decoder = new TextDecoder();
  let buffer = "";
  let accumulatedThinking = "";
  let accumulatedContent = "";
  let accumulatedTips = [];
  let messageId = null;
  let isThinking = true;

  try {
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (!line.trim() || !line.startsWith("data:")) continue;

        try {
          // 处理 "data:" 或 "data: " 两种格式
          const jsonStr = line.startsWith("data: ") ? line.slice(6) : line.slice(5);
          const data = JSON.parse(jsonStr);

          // 移除错误检查逻辑，因为错误响应不会进入 SSE 流处理

          // 处理新的数据格式：{"content":null,"thinkingContent":"We","tips":null,"id":"msg_123"}
          if (data.hasOwnProperty('thinkingContent') || data.hasOwnProperty('content') || data.hasOwnProperty('tips') || data.hasOwnProperty('id')) {
            // 处理 ID 字段
            if (data.id !== null && data.id !== undefined) {
              messageId = data.id;
              onChunk({ type: "id", content: data.id });
            }

            if (data.thinkingContent !== null && data.thinkingContent !== undefined) {
              // 思考内容
              accumulatedThinking += data.thinkingContent;
              onChunk({ type: "thought", content: data.thinkingContent });
            } else if (data.content !== null && data.content !== undefined) {
              // 主要内容
              if (isThinking) {
                // 从思考阶段切换到内容阶段
                isThinking = false;
                onChunk({ type: "separator" });
              }
              accumulatedContent += data.content;
              onChunk({ type: "text", content: data.content });
            }

            // 处理 tips 字段
            if (data.tips !== null && data.tips !== undefined && Array.isArray(data.tips)) {
              accumulatedTips = data.tips;
              onChunk({ type: "tips", content: data.tips });
            }
          } else {
            // 兼容旧格式：带有 type 字段的格式
            switch (data.type) {
              case "start":
                // 开始接收数据
                console.log("Stream started");
                break;
              case "thinking":
                // 思考过程
                if (data.thinkingContent) {
                  accumulatedThinking += data.thinkingContent;
                  onChunk({ type: "thought", content: data.thinkingContent });
                }
                break;
              case "thinking_complete":
                // 思考完成，切换到回复阶段
                isThinking = false;
                onChunk({ type: "separator" });
                break;
              case "content":
                // 主要内容
                if (data.content) {
                  accumulatedContent += data.content;
                  onChunk({ type: "text", content: data.content });
                }
                break;
              case "complete":
                // 数据接收完成
                console.log("Stream completed");
                return {
                  thoughts: data.thinkingContent || accumulatedThinking,
                  content: data.content || accumulatedContent,
                  tips: data.tips || accumulatedTips,
                  id: messageId,
                };
            }
          }
        } catch (e) {
          console.error("Error parsing SSE message:", e, "Line:", line);
        }
      }
    }
    
    return {
      thoughts: accumulatedThinking,
      content: accumulatedContent,
      tips: accumulatedTips,
      id: messageId,
    };
  } catch (error) {
    if (error.name === "AbortError") {
      // 不需要在这里取消reader，因为fetch已经被取消了
      throw error;
    }
    throw error;
  }
}
