.header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-bottom: 8px;
  padding: 0 16px; /* header也需要padding保持对齐 */
}

.conversationList {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 16px; /* 自身添加左右padding */

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(---, rgba(0, 0, 0, 0.06));
    border-radius: 3px;

    // &:hover {
    //   background: #64748b;
    // }
  }
}

.action {
  cursor: pointer;
  margin-left: 4px;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;

  div {
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.45);
    flex-shrink: 0;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }
}

.loadingMore,
.loadingState {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.endMessage {
  text-align: center;
  padding: 16px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
}

.noMoreData {
  text-align: center;
  padding: 16px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
}

.emptyState {
  text-align: center;
  padding: 32px 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.conversationItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 9px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  // border: 1px solid transparent;

  &:hover {
    background: var(---, rgba(0, 0, 0, 0.04));
  }

  &.active {
    background: rgba(186, 209, 255, 0.4);
  }
}

.conversationContent {
  flex: 1;
  cursor: pointer;
  min-width: 0;
}

.conversationTitle {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: var(---, rgba(0, 0, 0, 0.88));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebarWrapper {
  width: 248px;
  height: 100%;
  padding: 12px 16px 0 16px;
  transition: width 0.2s ease;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
}

.sidebarCollapsed {
  width: 0;
  padding: 0;
  margin: 0;
  visibility: hidden;
}

.contentLeftHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8ebed;
  // width: 216px;
}

.conAddBtn {
  width: 216px;
  border-radius: 8px;
  border: 1px solid rgba(61, 104, 245, 0.4);
  background: rgba(61, 104, 245, 0.04);
  display: flex;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  color: #3d68f5;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  cursor: pointer;
  box-sizing: border-box;
  svg {
    margin-right: 8px;
  }

  &:hover {
    border: 1px solid rgba(61, 104, 245, 0.4);
    background: rgba(61, 104, 245, 0.12);
  }
}

.foldBtn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }
  img {
    width: 18px;
    height: 18px;
  }
}

.contentLeftContent {
  margin-top: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-left: -16px; /* 抵消父容器的左padding */
  margin-right: -16px; /* 抵消父容器的右padding */
}
