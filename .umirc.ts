import { defineConfig } from 'umi';
import { routesConfig } from './routes'

// 获取当前环境
// const env = process.env.UMI_APP_ENV;
console.log('NODE_ENV=================>', process.env.NODE_ENV)
console.log('UMI_APP_ENV=================>', process.env.UMI_APP_ENV)

// 环境配置映射
const envConfigs = {
  // development: {
  //   define: {
  //     'process.env.API_BASE_URL': 'http://localhost:3011',
  //     'process.env.APP_ENV': 'dev',
  //     'process.env.VERSION': '1.0.0-dev',
  //     'process.env.DEBUG': true,
  //     'process.env.MOCK_ENABLED': true,
  //   },
  //   mock: {
  //     include: ['src/mock/**/*.ts'],
  //   },
  //   fastRefresh: true,
  //   devtool: 'eval-cheap-module-source-map' as const,
  // },
  // production: {
  //   define: {
  //     'process.env.API_BASE_URL': 'https://ai.tendata.cn',
  //     'process.env.APP_ENV': 'prod',
  //     'process.env.VERSION': '1.0.0',
  //     'process.env.DEBUG': false,
  //     'process.env.MOCK_ENABLED': false,
  //   },
  //   mock: false,
  //   hash: true,
  //   devtool: false,
  //   jsMinifier: 'terser',
  //   cssMinifier: 'cssnano',
  // },
};

// 获取当前环境的配置,上面的env可以在外部通过cross-env自定义定义
// const currentEnvConfig = envConfigs[env as keyof typeof envConfigs] || envConfigs.development;

export default defineConfig({
  plugins: ['@umijs/plugins/dist/locale', '@umijs/plugins/dist/dva', '@umijs/plugins/dist/request'],
  routes: routesConfig,
  npmClient: 'pnpm',
  proxy: {
    '/api/auth': {
      'target': 'https://account.tendata.net/',
      'changeOrigin': true,
    },
    '/api/ai': {
      'target': 'https://ai.tendata.net',
      'changeOrigin': true,
    },
    '/api/bizr': {
      'target': 'https://bizr.tendata.net',
      'changeOrigin': true,
    },
    '/api/feedback': {
      'target': 'https://data.tendata.net',
      'changeOrigin': true,
    }
  },
  favicons: [
    'https://static.tendata.net/assets/common/logo.ico'
  ],
  locale: {
    antd: false,
    baseNavigator: true,
    baseSeparator: '_',
    default: 'zh_CN',
    title: false,
    useLocalStorage: true,
  },
  dva: {},
  request: {},
  // 合并当前环境的配置
  // ...(currentEnvConfig as any),
});
