import type { RequestConfig } from 'umi';
const appEnv = process.env.UMI_APP_ENV || 'dev';

// 初始化诸葛io埋点
function initZhuge() {
  // 只在生产环境或指定环境加载诸葛io
  if (appEnv === 'prod' || appEnv === 'uat') {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.id = 'zhuge-js';
    script.async = true;
    script.src = 'https://su.zhugeio.com/zhuge.js?v=2025630';
    document.head.appendChild(script);

    // 可以在这里添加诸葛io的初始化配置
    script.onload = () => {
      // 如果需要初始化配置，可以在这里添加
      // window.zhuge && window.zhuge.init('your-app-key');
    };
  }
}

// 在应用启动时初始化诸葛io
if (typeof window !== 'undefined') {
  initZhuge();
}

export const request: RequestConfig = {
  // other axios options you want
  withCredentials: true,
  errorConfig: {
    errorHandler(error: any) {
      // 可以在这里统一处理 HTTP 错误
      const { response }  = error;
      if (response && response.status) {
        console.log('请求失败，失败原因 ==================>:', response.status , response.statusText);
        let host = 'https://login.tendata.net/login'
        if (appEnv === 'prod') host = 'https://login.tendata.cn/login';
        if (response.status === 401 && appEnv !== 'dev') window.location.href = host;
      }
    },
    errorThrower(){
    }
  },
  requestInterceptors: [
    // (url: any, options: { headers: any; }) => {
    //   // 请求拦截器：在发送请求前，统一添加 Authorization header
    //   const token = localStorage.getItem('token');
    //   if (token) {
    //     options.headers = {
    //       ...options.headers,
    //       Authorization: `Bearer ${token}`,
    //     };
    //   }
    //   return { url, options };
    // },
  ],
};