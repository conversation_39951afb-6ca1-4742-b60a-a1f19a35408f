import type { RequestConfig } from 'umi';
const appEnv = process.env.UMI_APP_ENV || 'dev';

export const request: RequestConfig = {
  // other axios options you want
  withCredentials: true,
  errorConfig: {
    errorHandler(error: any) {
      // 可以在这里统一处理 HTTP 错误
      const { response }  = error;
      if (response && response.status) {
        console.log('请求失败，失败原因 ==================>:', response.status , response.statusText);
        let host = 'https://login.tendata.net/login'
        if (appEnv === 'prod') host = 'https://login.tendata.cn/login';
        if (response.status === 401 && appEnv !== 'dev') window.location.href = host;
      }
    },
    errorThrower(){
    }
  },
  requestInterceptors: [
    // (url: any, options: { headers: any; }) => {
    //   // 请求拦截器：在发送请求前，统一添加 Authorization header
    //   const token = localStorage.getItem('token');
    //   if (token) {
    //     options.headers = {
    //       ...options.headers,
    //       Authorization: `Bearer ${token}`,
    //     };
    //   }
    //   return { url, options };
    // },
  ],
};