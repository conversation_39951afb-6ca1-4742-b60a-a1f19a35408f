/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-body: #F7F7FC;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white: #fff;
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-body);
  --color-background-soft: var(--vt-c-white-soft);
}

s*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}
ul, li {
  margin: 0;
}

body {
  margin: 0;
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    PingFang SC,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 14px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
