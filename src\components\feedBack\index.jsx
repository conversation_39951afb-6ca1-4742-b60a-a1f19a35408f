import React, { useState } from "react";
import { Dropdown, But<PERSON>, message } from "antd";
import { isCN } from "@/utils/index";
import { FeedBackIcon, CloseIcon, SuccessIcon } from "@/components/icon";
import { FormattedMessage } from "umi";
import { platformFeedback } from "@/services/chat";
import styles from "./index.module.less";

const FeedBack = () => {
  const [open, setOpen] = useState(false);
  const [rating, setRating] = useState(0);
  const [suggestion, setSuggestion] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) {
      message.info(isCN ? "请选择反馈评分" : "Please select a rating.");
      return;
    }

    const params = {
      scene: "AI",
      homepage: "AI_HOME",
      params: [
        {
          rating: rating,
          customerRemark: suggestion,
        },
      ],
    };

    const res = await platformFeedback(params);
    if (!res?.id)
      return message.error(
        isCN
          ? "系统繁忙，请稍后再试"
          : "The system is busy. Please try again later."
      );

    setIsSubmitted(true);

    // 3秒后自动关闭
    setTimeout(() => {
      setOpen(false);
      setIsSubmitted(false);
      setRating(0);
      setSuggestion("");
    }, 3000);
  };

  const handleCancel = () => {
    setOpen(false);
    setRating(0);
    setSuggestion("");
    setIsSubmitted(false);
  };

   const handleChange = (e) => {
    const value = e.target.value;
    // 限制最大字符数
    if (value.length <= 500) {
      setSuggestion(value);
    }
  };

  // 自定义下拉内容
  const popupRender = () => (
    <div className={styles.feedbackDropdown}>
      <CloseIcon className={styles.closeIcon} onClick={handleCancel} />

      <div className={styles.title}>
        <FormattedMessage id="feedback-title" />
      </div>
      <div className={styles.subDesc}>
        <FormattedMessage id="feedback-sub-desc" />
      </div>

      {isSubmitted ? (
        <div className={styles.thankYou}>
          <SuccessIcon />
          <div className={styles.thankYouText}>
            <FormattedMessage id="feedback-thank-you" />
          </div>
        </div>
      ) : (
        <>
          <div className={styles.ratingSection}>
            <div className={styles.ratingLabel}>
              <span className={styles.required}>*</span>
              <FormattedMessage id="feedback-rating-label" />
            </div>
            <div className={styles.ratingButtons}>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((score) => (
                <div
                  key={score}
                  className={`${styles.ratingButton} ${rating === score ? styles.ratingButtonActive : ""}`}
                  onClick={() => setRating(score)}
                >
                  {score}
                </div>
              ))}
            </div>
          </div>

          <div className={styles.suggestionSection}>
            <div className={styles.suggestionLabel}>
              <FormattedMessage id="feedback-suggestion-label" />
            </div>
            <textarea
              className={styles.suggestionTextarea}
              placeholder={
                isCN
                  ? "填写反馈问题/功能建议"
                  : "Please enter your questions or feature suggestions"
              }
              value={suggestion}
              onChange={handleChange}
              rows={4}
            />
          </div>

          <div className={styles.buttonGroup}>
            <Button onClick={handleCancel}>
              <FormattedMessage id="feedback-cancel" />
            </Button>
            <Button type="primary" onClick={handleSubmit}>
              <FormattedMessage id="feedback-submit" />
            </Button>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      open={open}
      onOpenChange={setOpen}
      popupRender={popupRender}
      trigger={["click"]}
      placement="leftBottom"
    >
      <div className={styles.feedBack} style={{ height: isCN ? 86 : 111 }}>
        <FeedBackIcon />
        <span className={isCN ? styles.text : styles.enText}>
          <FormattedMessage id="feedback" />
        </span>
      </div>
    </Dropdown>
  );
};

export default FeedBack;
